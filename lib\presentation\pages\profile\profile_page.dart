import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'dart:io';

import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../../core/di/injection_container.dart';
import '../../bloc/profile/profile_bloc.dart';
import '../../bloc/profile/profile_event.dart';
import '../../bloc/profile/profile_state.dart';
import '../../bloc/auth/auth_bloc.dart';
import '../../bloc/auth/auth_event.dart';

import '../../widgets/profile/profile_avatar.dart';
import '../../widgets/profile/password_change_form.dart';
import '../../widgets/profile/delete_account_dialog.dart';

class ProfilePage extends StatelessWidget {
  const ProfilePage({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => getIt<ProfileBloc>()..add(ProfileLoadRequested()),
      child: const _ProfilePageContent(),
    );
  }
}

class _ProfilePageContent extends StatefulWidget {
  const _ProfilePageContent();

  @override
  State<_ProfilePageContent> createState() => _ProfilePageContentState();
}

class _ProfilePageContentState extends State<_ProfilePageContent> {
  bool _isPasswordFormVisible = false;
  bool _isEmailEditing = false;
  bool _isNameEditing = false;
  final _emailController = TextEditingController();
  final _nameController = TextEditingController();

  @override
  void dispose() {
    _emailController.dispose();
    _nameController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.teal,
      body: BlocConsumer<ProfileBloc, ProfileState>(
        listener: (context, state) {
          if (state is ProfileError) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: AppColors.error,
              ),
            );
          } else if (state is ProfileUpdateSuccess) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: AppColors.success,
              ),
            );
            // Reset editing states
            setState(() {
              _isEmailEditing = false;
              _isNameEditing = false;
              _isPasswordFormVisible = false;
            });
          } else if (state is ProfileAccountDeleted) {
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text(state.message),
                backgroundColor: AppColors.success,
              ),
            );
            // Sign out and navigate to auth
            context.read<AuthBloc>().add(AuthSignOutRequested());
            context.go('/auth');
          }
        },
        builder: (context, state) {
          if (state is ProfileLoading) {
            return const Center(
              child: CircularProgressIndicator(color: AppColors.teal),
            );
          }

          if (state is ProfileError && state.user == null) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.error_outline,
                    size: 64,
                    color: AppColors.error,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Erreur de chargement',
                    style: AppTextStyles.headlineMedium,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    state.message,
                    style: AppTextStyles.bodyMedium,
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      context.read<ProfileBloc>().add(ProfileLoadRequested());
                    },
                    child: const Text('Réessayer'),
                  ),
                ],
              ),
            );
          }

          // Get user data from state
          final user = _getUserFromState(state);
          final avatarUrl = _getAvatarUrlFromState(state);
          final isUpdating = state is ProfileUpdating;

          if (user == null) {
            return const Center(
              child: Text('Aucune donnée utilisateur disponible'),
            );
          }

          return Column(
            children: [
              // Header with teal background
              Container(
                padding: const EdgeInsets.fromLTRB(20, 60, 20, 20),
                child: Row(
                  children: [
                    GestureDetector(
                      onTap: () => context.pop(),
                      child: Container(
                        width: 48,
                        height: 48,
                        decoration: BoxDecoration(
                          color: Colors.white.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: const Icon(
                          Icons.arrow_back,
                          color: Colors.white,
                          size: 24,
                        ),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Text(
                        'Profil',
                        style: AppTextStyles.headlineMedium.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              // Content container with white background
              Expanded(
                child: Container(
                  width: double.infinity,
                  decoration: BoxDecoration(
                    color: AppColors.white,
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(24),
                      topRight: Radius.circular(24),
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.1),
                        blurRadius: 8,
                        offset: const Offset(0, -4),
                      ),
                    ],
                  ),
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      children: [
                        // Avatar & Profile Card
                        _buildAvatarCard(user, avatarUrl, isUpdating),
                        const SizedBox(height: 16),

                        // Account Info Card
                        _buildAccountInfoCard(user),
                        const SizedBox(height: 16),

                        // Security Card
                        _buildSecurityCard(user, isUpdating),
                        const SizedBox(height: 16),

                        // Danger Zone Card
                        _buildDangerZoneCard(isUpdating),
                        const SizedBox(
                            height: 100), // Bottom padding for navigation
                      ],
                    ),
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  // Helper methods to extract data from different states
  dynamic _getUserFromState(ProfileState state) {
    if (state is ProfileLoaded) return state.user;
    if (state is ProfileUpdating) return state.user;
    if (state is ProfileUpdateSuccess) return state.user;
    if (state is ProfileError) return state.user;
    return null;
  }

  String? _getAvatarUrlFromState(ProfileState state) {
    if (state is ProfileLoaded) return state.avatarUrl;
    if (state is ProfileUpdating) return state.avatarUrl;
    if (state is ProfileUpdateSuccess) return state.avatarUrl;
    if (state is ProfileError) return state.avatarUrl;
    return null;
  }

  Widget _buildAvatarCard(user, String? avatarUrl, bool isUpdating) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            Row(
              children: [
                Icon(
                  Icons.person,
                  color: AppColors.teal,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'Avatar & Profil',
                  style: AppTextStyles.titleMedium.copyWith(
                    color: AppColors.navy,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                ProfileAvatar(
                  user: user,
                  avatarUrl: avatarUrl,
                  isEditable: !isUpdating,
                  size: 80,
                  onImageSelected: (File imageFile) {
                    context.read<ProfileBloc>().add(
                          ProfileAvatarUpdateRequested(imageFile: imageFile),
                        );
                  },
                  onRemoveImage: () {
                    context
                        .read<ProfileBloc>()
                        .add(ProfileAvatarRemoveRequested());
                  },
                ),
                const SizedBox(width: 20),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Name editing
                      if (_isNameEditing) ...[
                        TextField(
                          controller: _nameController,
                          decoration: InputDecoration(
                            labelText: 'Nom',
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                            contentPadding: const EdgeInsets.symmetric(
                              horizontal: 12,
                              vertical: 8,
                            ),
                          ),
                          style: AppTextStyles.bodyMedium,
                        ),
                        const SizedBox(height: 8),
                        Row(
                          children: [
                            TextButton(
                              onPressed: isUpdating
                                  ? null
                                  : () {
                                      setState(() {
                                        _isNameEditing = false;
                                      });
                                    },
                              child: const Text('Annuler'),
                            ),
                            const SizedBox(width: 8),
                            ElevatedButton(
                              onPressed: isUpdating
                                  ? null
                                  : () {
                                      if (_nameController.text.isNotEmpty) {
                                        context.read<ProfileBloc>().add(
                                              ProfileNameUpdateRequested(
                                                  name: _nameController.text),
                                            );
                                      }
                                    },
                              style: ElevatedButton.styleFrom(
                                backgroundColor: AppColors.teal,
                                foregroundColor: AppColors.white,
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 16,
                                  vertical: 8,
                                ),
                              ),
                              child: isUpdating
                                  ? const SizedBox(
                                      height: 16,
                                      width: 16,
                                      child: CircularProgressIndicator(
                                        color: AppColors.white,
                                        strokeWidth: 2,
                                      ),
                                    )
                                  : const Text('Enregistrer'),
                            ),
                          ],
                        ),
                      ] else ...[
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                user.displayName,
                                style: AppTextStyles.titleMedium.copyWith(
                                  color: AppColors.navy,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                            IconButton(
                              onPressed: isUpdating
                                  ? null
                                  : () {
                                      _nameController.text = user.name ?? '';
                                      setState(() {
                                        _isNameEditing = true;
                                      });
                                    },
                              icon: const Icon(Icons.edit, size: 18),
                              color: AppColors.teal,
                            ),
                          ],
                        ),
                      ],
                      Text(
                        user.email,
                        style: AppTextStyles.bodyMedium.copyWith(
                          color: AppColors.grey600,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAccountInfoCard(user) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: AppColors.teal,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'Informations du compte',
                  style: AppTextStyles.titleMedium.copyWith(
                    color: AppColors.navy,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Date de création',
                        style: AppTextStyles.labelMedium.copyWith(
                          color: AppColors.grey600,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        user.createdAt != null
                            ? DateFormat('dd/MM/yyyy à HH:mm')
                                .format(user.createdAt!)
                            : 'Inconnue',
                        style: AppTextStyles.bodyMedium,
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Dernière mise à jour',
                        style: AppTextStyles.labelMedium.copyWith(
                          color: AppColors.grey600,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        user.updatedAt != null
                            ? DateFormat('dd/MM/yyyy à HH:mm')
                                .format(user.updatedAt!)
                            : 'Inconnue',
                        style: AppTextStyles.bodyMedium,
                      ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Divider(color: AppColors.grey200),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Foyer',
                        style: AppTextStyles.labelMedium.copyWith(
                          color: AppColors.grey600,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        user.householdName ?? 'Aucun foyer',
                        style: AppTextStyles.bodyMedium,
                      ),
                    ],
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: () {
                    context.read<AuthBloc>().add(AuthSignOutRequested());
                    context.go('/auth');
                  },
                  icon: const Icon(Icons.logout, size: 18),
                  label: const Text('Déconnexion'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.teal,
                    foregroundColor: AppColors.white,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 8,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSecurityCard(user, bool isUpdating) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.security,
                  color: AppColors.teal,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'Sécurité',
                  style: AppTextStyles.titleMedium.copyWith(
                    color: AppColors.navy,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Email section
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Adresse email',
                        style: AppTextStyles.labelMedium.copyWith(
                          color: AppColors.grey700,
                        ),
                      ),
                      const SizedBox(height: 4),
                      if (_isEmailEditing) ...[
                        TextField(
                          controller: _emailController,
                          decoration: InputDecoration(
                            hintText: 'Nouvelle adresse email',
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                            contentPadding: const EdgeInsets.symmetric(
                              horizontal: 12,
                              vertical: 8,
                            ),
                          ),
                          style: AppTextStyles.bodyMedium,
                        ),
                        const SizedBox(height: 8),
                        Row(
                          children: [
                            TextButton(
                              onPressed: isUpdating
                                  ? null
                                  : () {
                                      setState(() {
                                        _isEmailEditing = false;
                                      });
                                    },
                              child: const Text('Annuler'),
                            ),
                            const SizedBox(width: 8),
                            ElevatedButton(
                              onPressed: isUpdating
                                  ? null
                                  : () {
                                      if (_emailController.text.isNotEmpty) {
                                        context.read<ProfileBloc>().add(
                                              ProfileEmailUpdateRequested(
                                                  email: _emailController.text),
                                            );
                                      }
                                    },
                              style: ElevatedButton.styleFrom(
                                backgroundColor: AppColors.teal,
                                foregroundColor: AppColors.white,
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 16,
                                  vertical: 8,
                                ),
                              ),
                              child: isUpdating
                                  ? const SizedBox(
                                      height: 16,
                                      width: 16,
                                      child: CircularProgressIndicator(
                                        color: AppColors.white,
                                        strokeWidth: 2,
                                      ),
                                    )
                                  : const Text('Enregistrer'),
                            ),
                          ],
                        ),
                      ] else ...[
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                user.email,
                                style: AppTextStyles.bodyMedium,
                              ),
                            ),
                            TextButton(
                              onPressed: isUpdating
                                  ? null
                                  : () {
                                      _emailController.text = user.email;
                                      setState(() {
                                        _isEmailEditing = true;
                                      });
                                    },
                              child: const Text('Modifier'),
                            ),
                          ],
                        ),
                      ],
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Divider(color: AppColors.grey200),
            const SizedBox(height: 16),

            // Password section
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Mot de passe',
                        style: AppTextStyles.labelMedium.copyWith(
                          color: AppColors.grey700,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Modifier votre mot de passe',
                        style: AppTextStyles.bodySmall.copyWith(
                          color: AppColors.grey600,
                        ),
                      ),
                    ],
                  ),
                ),
                ElevatedButton(
                  onPressed: isUpdating
                      ? null
                      : () {
                          setState(() {
                            _isPasswordFormVisible = !_isPasswordFormVisible;
                          });
                        },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: _isPasswordFormVisible
                        ? AppColors.grey400
                        : AppColors.teal,
                    foregroundColor: AppColors.white,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 8,
                    ),
                  ),
                  child: Text(_isPasswordFormVisible ? 'Annuler' : 'Modifier'),
                ),
              ],
            ),
            if (_isPasswordFormVisible) ...[
              const SizedBox(height: 16),
              PasswordChangeForm(
                isLoading: isUpdating,
                onPasswordChange: (currentPassword, newPassword) {
                  context.read<ProfileBloc>().add(
                        ProfilePasswordChangeRequested(
                          currentPassword: currentPassword,
                          newPassword: newPassword,
                        ),
                      );
                },
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildDangerZoneCard(bool isUpdating) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(color: AppColors.error.withOpacity(0.3)),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.warning,
                  color: AppColors.error,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'Zone de danger',
                  style: AppTextStyles.titleMedium.copyWith(
                    color: AppColors.error,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Supprimer mon compte',
                        style: AppTextStyles.titleSmall.copyWith(
                          color: AppColors.grey900,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Toutes vos données personnelles seront supprimées définitivement',
                        style: AppTextStyles.bodySmall.copyWith(
                          color: AppColors.grey600,
                        ),
                      ),
                    ],
                  ),
                ),
                ElevatedButton.icon(
                  onPressed: isUpdating
                      ? null
                      : () {
                          showDialog(
                            context: context,
                            builder: (context) => DeleteAccountDialog(
                              isLoading: isUpdating,
                              onConfirmDelete: (password) {
                                Navigator.of(context).pop();
                                context.read<ProfileBloc>().add(
                                      ProfileAccountDeleteRequested(
                                          password: password),
                                    );
                              },
                            ),
                          );
                        },
                  icon: const Icon(Icons.delete_forever, size: 18),
                  label: const Text('Supprimer'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.error,
                    foregroundColor: AppColors.white,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 8,
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
