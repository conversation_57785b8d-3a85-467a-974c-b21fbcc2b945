/// Application configuration constants
class AppConfig {
  // Supabase Configuration - Matching web app
  static const String supabaseUrl = 'https://wzzykbnebhyvdoagpwvk.supabase.co';
  static const String supabaseAnonKey =
      'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Ind6enlrYm5lYmh5dmRvYWdwd3ZrIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDUwMTMzNjMsImV4cCI6MjA2MDU4OTM2M30.LYLDFRLZB4qtuzxTjRuZqnepRwrg3BNGLwdnwU4D0Es';

  // App Information
  static const String appName = 'MedyTrack Mobile';
  static const String appVersion = '0.4.5';
  static const String appBuildNumber = '5';

  // API Configuration
  static const int apiTimeoutSeconds = 30;
  static const int maxRetryAttempts = 3;

  // Local Storage Keys
  static const String userTokenKey = 'user_token';
  static const String userProfileKey = 'user_profile';
  static const String onboardingCompletedKey = 'onboarding_completed';
  static const String themePreferenceKey = 'theme_preference';

  // Feature Flags
  static const bool enableNotifications = true;
  static const bool enableBiometricAuth = true;
  static const bool enableOfflineMode = true;
  static const bool enableAnalytics = false;

  // Medicine Configuration
  static const int defaultExpiryWarningMonths = 1;
  static const int defaultLowStockThreshold = 5;
  static const int maxMedicineNameLength = 100;
  static const int maxNotesLength = 500;

  // UI Configuration
  static const double defaultBorderRadius = 16.0;
  static const double defaultPadding = 20.0;
  static const double defaultMargin = 16.0;
  static const int animationDurationMs = 300;

  // Pagination
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;

  // Cache Configuration
  static const int cacheExpirationHours = 24;
  static const int maxCacheSize = 100; // MB

  // Validation Rules
  static const int minPasswordLength = 8;
  static const int maxPasswordLength = 128;
  static const int minNameLength = 2;
  static const int maxNameLength = 50;

  // Environment Detection
  static bool get isDebug {
    bool inDebugMode = false;
    assert(inDebugMode = true);
    return inDebugMode;
  }

  static bool get isProduction => !isDebug;

  // Logging Configuration
  static bool get enableLogging => isDebug;
  static bool get enableVerboseLogging => isDebug;
}
