import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';

import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../bloc/auth/auth_bloc.dart';
import '../../bloc/auth/auth_event.dart';
import '../../bloc/auth/auth_state.dart' as auth_state;
import '../../bloc/settings/settings_bloc.dart';
import '../../bloc/settings/settings_event.dart';
import '../../bloc/settings/settings_state.dart';

import '../../widgets/settings/settings_section.dart';
import '../../widgets/settings/settings_tile.dart';
import '../../widgets/settings/enhanced_settings_tile.dart';
import '../../widgets/settings/toggle_setting.dart';

class SettingsPage extends StatefulWidget {
  const SettingsPage({super.key});

  @override
  State<SettingsPage> createState() => _SettingsPageState();
}

class _SettingsPageState extends State<SettingsPage> {
  @override
  void initState() {
    super.initState();
    // Load settings when page initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final authState = context.read<AuthBloc>().state;
      if (authState is auth_state.AuthAuthenticated) {
        context.read<SettingsBloc>().add(
              SettingsLoadRequested(userId: authState.user.id),
            );
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        children: [
          // Integrated header with gradient background
          Container(
            width: double.infinity,
            decoration: BoxDecoration(
              gradient: _getHeaderGradient(),
              borderRadius: const BorderRadius.only(
                bottomLeft: Radius.circular(32),
                bottomRight: Radius.circular(32),
              ),
            ),
            child: SafeArea(
              bottom: false,
              child: Padding(
                padding: const EdgeInsets.fromLTRB(20, 20, 20, 40),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Integrated header card
                    _buildIntegratedHeaderCard(),
                  ],
                ),
              ),
            ),
          ),

          Expanded(
            child: BlocBuilder<AuthBloc, auth_state.AuthState>(
              builder: (context, authState) {
                if (authState is! auth_state.AuthAuthenticated) {
                  return const Center(
                    child: CircularProgressIndicator(),
                  );
                }

                return BlocConsumer<SettingsBloc, SettingsState>(
                  listener: (context, settingsState) {
                    if (settingsState is SettingsError) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text(settingsState.message),
                          backgroundColor: AppColors.error,
                        ),
                      );
                    } else if (settingsState is SettingsOperationSuccess) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text(settingsState.message),
                          backgroundColor: AppColors.success,
                        ),
                      );
                    }
                  },
                  builder: (context, settingsState) {
                    return SingleChildScrollView(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          _buildUserSection(context, authState.user),
                          const SizedBox(height: 24),
                          _buildPreferencesSection(context, settingsState),
                          const SizedBox(height: 24),
                          _buildPersonalizationSection(context),
                          const SizedBox(height: 24),
                          // Data section hidden as requested
                          // _buildDataSection(context, settingsState),
                          // const SizedBox(height: 24),
                          // Debug section hidden for production release
                          // _buildDebugSection(context),
                          // const SizedBox(height: 24),
                          _buildAboutSection(),
                          const SizedBox(height: 32),
                          _buildLogoutButton(context),
                        ],
                      ),
                    );
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUserSection(BuildContext context, dynamic user) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Row(
          children: [
            CircleAvatar(
              radius: 30,
              backgroundColor: AppColors.teal.withValues(alpha: 0.1),
              child: Text(
                user.initials,
                style: AppTextStyles.headlineMedium.copyWith(
                  color: AppColors.teal,
                ),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    user.displayName ?? 'Utilisateur',
                    style: AppTextStyles.titleLarge,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    user.email,
                    style: AppTextStyles.bodyMedium.copyWith(
                      color: AppColors.grey600,
                    ),
                  ),
                ],
              ),
            ),
            IconButton(
              onPressed: () => context.push('/profile'),
              icon: const Icon(Icons.edit),
              style: IconButton.styleFrom(
                backgroundColor: AppColors.grey100,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPreferencesSection(
      BuildContext context, SettingsState settingsState) {
    final settings =
        settingsState is SettingsLoaded ? settingsState.settings : null;
    final isLoading =
        settingsState is SettingsLoading || settingsState is SettingsUpdating;

    return SettingsSection(
      title: 'Préférences',
      children: [
        EnhancedSettingsTile(
          icon: Icons.notifications_outlined,
          title: 'Notifications',
          subtitle: 'Alertes d\'expiration et rappels',
          onTap: () => context.push('/settings/notifications'),
          trailing: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                settings?.notifications.pushNotifications == true
                    ? 'Activées'
                    : 'Désactivées',
                style: AppTextStyles.bodySmall.copyWith(
                  color: AppColors.grey600,
                ),
              ),
              const SizedBox(width: 8),
              Icon(
                Icons.chevron_right,
                color: AppColors.grey400,
                size: 20,
              ),
            ],
          ),
        ),
        EnhancedSettingsTile(
          icon: Icons.language_outlined,
          title: 'Langue et région',
          subtitle: _getLanguageDisplayName(settings?.app.language ?? 'fr'),
          onTap: () => context.push('/settings/language'),
          trailing: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                _getLanguageFlag(settings?.app.language ?? 'fr'),
                style: const TextStyle(fontSize: 16),
              ),
              const SizedBox(width: 8),
              Icon(
                Icons.chevron_right,
                color: AppColors.grey400,
                size: 20,
              ),
            ],
          ),
        ),
        ToggleSetting(
          icon: Icons.palette_outlined,
          title: 'Mode sombre',
          subtitle: 'Utiliser le thème sombre',
          value: settings?.app.isDarkMode ?? false,
          onChanged: (value) {
            if (settings != null) {
              context.read<SettingsBloc>().add(
                    ThemeUpdateRequested(
                      userId: settings.userId,
                      isDarkMode: value,
                    ),
                  );
            }
          },
          enabled: settings != null,
          isLoading: isLoading &&
              settingsState is SettingsUpdating &&
              settingsState.section == 'app',
        ),
        EnhancedSettingsTile(
          icon: Icons.schedule_outlined,
          title: 'Format de date',
          subtitle: settings?.app.dateFormat ?? 'DD/MM/YYYY',
          onTap: () => _showDateFormatSelection(context, settings),
          enabled: settings != null,
          isLoading: isLoading &&
              settingsState is SettingsUpdating &&
              settingsState.section == 'app',
        ),
      ],
    );
  }

  Widget _buildPersonalizationSection(BuildContext context) {
    return SettingsSection(
      title: 'Personnalisation',
      children: [
        EnhancedSettingsTile(
          icon: Icons.tune_outlined,
          title: 'Préférences avancées',
          subtitle: 'Famille, emplacements, étiquettes',
          onTap: () => context.push('/settings/personalization'),
        ),
      ],
    );
  }

  Widget _buildDataSection(BuildContext context, SettingsState settingsState) {
    final settings =
        settingsState is SettingsLoaded ? settingsState.settings : null;
    final isLoading =
        settingsState is SettingsLoading || settingsState is SettingsUpdating;

    return SettingsSection(
      title: 'Données',
      children: [
        ToggleSetting(
          icon: Icons.cloud_sync_outlined,
          title: 'Synchronisation automatique',
          subtitle: 'Synchroniser avec le cloud',
          value: settings?.app.autoSync ?? true,
          onChanged: (value) {
            if (settings != null) {
              final updatedApp = settings.app.copyWith(autoSync: value);
              context.read<SettingsBloc>().add(
                    AppSettingsUpdateRequested(
                      userId: settings.userId,
                      settings: updatedApp,
                    ),
                  );
            }
          },
          enabled: settings != null,
          isLoading: isLoading &&
              settingsState is SettingsUpdating &&
              settingsState.section == 'app',
        ),
        EnhancedSettingsTile(
          icon: Icons.cloud_outlined,
          title: 'Données & Sauvegarde',
          subtitle: 'Export, import, synchronisation',
          onTap: () => context.push('/settings/data'),
          trailing: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                settings?.app.autoSync == true
                    ? 'Sync activée'
                    : 'Sync désactivée',
                style: AppTextStyles.bodySmall.copyWith(
                  color: AppColors.grey600,
                ),
              ),
              const SizedBox(width: 8),
              Icon(
                Icons.chevron_right,
                color: AppColors.grey400,
                size: 20,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildDebugSection(BuildContext context) {
    return SettingsSection(
      title: 'Développement',
      children: [
        SettingsTile(
          icon: Icons.bug_report_outlined,
          title: 'Debug & Test',
          subtitle: 'Tester les fonctionnalités principales',
          onTap: () {
            context.push('/debug');
          },
        ),
        SettingsTile(
          icon: Icons.medication_outlined,
          title: 'Medicine Card Debug',
          subtitle: 'Debug status-based theming',
          onTap: () {
            context.push('/debug/medicine-cards');
          },
        ),
        SettingsTile(
          icon: Icons.add_box_outlined,
          title: 'Add Medicine Debug',
          subtitle: 'Test dual-path medicine addition workflow',
          onTap: () {
            context.push('/debug/add-medicine');
          },
        ),
      ],
    );
  }

  Widget _buildAboutSection() {
    return SettingsSection(
      title: 'À propos',
      children: [
        SettingsTile(
          icon: Icons.info_outlined,
          title: 'Version de l\'application',
          subtitle: 'v0.3.1 - Enhanced Settings',
          onTap: () => _showAppInfo(context),
        ),
        SettingsTile(
          icon: Icons.code_outlined,
          title: 'Développé avec Flutter',
          subtitle: 'Application mobile cross-platform',
          onTap: () => _showTechInfo(context),
        ),
        SettingsTile(
          icon: Icons.favorite_outlined,
          title: 'MedyTrack',
          subtitle: 'Votre compagnon santé intelligent',
          onTap: () => _showAboutApp(context),
        ),
      ],
    );
  }

  Widget _buildLogoutButton(BuildContext context) {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton.icon(
        onPressed: () => _showLogoutDialog(context),
        icon: const Icon(Icons.logout),
        label: const Text('Se déconnecter'),
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.error,
          foregroundColor: AppColors.white,
          padding: const EdgeInsets.symmetric(vertical: 16),
        ),
      ),
    );
  }

  void _showLogoutDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Se déconnecter'),
        content: const Text(
          'Êtes-vous sûr de vouloir vous déconnecter ?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Annuler'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              context.read<AuthBloc>().add(AuthSignOutRequested());
            },
            style: TextButton.styleFrom(foregroundColor: AppColors.error),
            child: const Text('Se déconnecter'),
          ),
        ],
      ),
    );
  }

  /// Gradient matching the white-blue background style
  LinearGradient _getHeaderGradient() {
    return const LinearGradient(
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
      colors: [
        Color(0xFFE3F0FF), // Light pastel blue
        Color(0xFFFFFFFF), // White bottom blend
      ],
    );
  }

  /// Integrated header card with title (no back button for main settings page)
  Widget _buildIntegratedHeaderCard() {
    return Container(
      margin: const EdgeInsets.only(top: 16),
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(24),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08),
            blurRadius: 20,
            offset: const Offset(0, 6),
          ),
        ],
      ),
      child: Text(
        'Paramètres',
        style: AppTextStyles.titleLarge.copyWith(
          color: AppColors.navy,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  // Helper methods
  String _getLanguageDisplayName(String languageCode) {
    switch (languageCode) {
      case 'fr':
        return 'Français';
      case 'en':
        return 'English';
      case 'ar':
        return 'العربية';
      default:
        return 'Français';
    }
  }

  String _getLanguageFlag(String languageCode) {
    switch (languageCode) {
      case 'fr':
        return '🇫🇷';
      case 'en':
        return '🇺🇸';
      case 'ar':
        return '🇹🇳';
      default:
        return '🇫🇷';
    }
  }

  void _showLanguageSelection(BuildContext context, dynamic settings) {
    if (settings == null) return;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Choisir la langue'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildLanguageOption(context, settings, 'fr', 'Français'),
            _buildLanguageOption(context, settings, 'en', 'English'),
            _buildLanguageOption(context, settings, 'ar', 'العربية'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Fermer'),
          ),
        ],
      ),
    );
  }

  Widget _buildLanguageOption(
      BuildContext context, dynamic settings, String code, String name) {
    return ListTile(
      title: Text(name),
      leading: Radio<String>(
        value: code,
        groupValue: settings.app.language,
        onChanged: (value) {
          context.read<SettingsBloc>().add(
                LanguageUpdateRequested(
                  userId: settings.userId,
                  language: value!,
                ),
              );
          Navigator.pop(context);
        },
        activeColor: AppColors.teal,
      ),
      onTap: () {
        context.read<SettingsBloc>().add(
              LanguageUpdateRequested(
                userId: settings.userId,
                language: code,
              ),
            );
        Navigator.pop(context);
      },
    );
  }

  void _showDateFormatSelection(BuildContext context, dynamic settings) {
    if (settings == null) return;

    final formats = ['DD/MM/YYYY', 'MM/DD/YYYY', 'YYYY-MM-DD'];

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Format de date'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: formats
              .map((format) => ListTile(
                    title: Text(format),
                    leading: Radio<String>(
                      value: format,
                      groupValue: settings.app.dateFormat,
                      onChanged: (value) {
                        final updatedApp =
                            settings.app.copyWith(dateFormat: value);
                        context.read<SettingsBloc>().add(
                              AppSettingsUpdateRequested(
                                userId: settings.userId,
                                settings: updatedApp,
                              ),
                            );
                        Navigator.pop(context);
                      },
                      activeColor: AppColors.teal,
                    ),
                    onTap: () {
                      final updatedApp =
                          settings.app.copyWith(dateFormat: format);
                      context.read<SettingsBloc>().add(
                            AppSettingsUpdateRequested(
                              userId: settings.userId,
                              settings: updatedApp,
                            ),
                          );
                      Navigator.pop(context);
                    },
                  ))
              .toList(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Fermer'),
          ),
        ],
      ),
    );
  }

  void _showBackupOptions(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Options de sauvegarde'),
        content: const Text(
            'La sauvegarde automatique est activée. Vos données sont synchronisées avec le cloud.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _exportSettings(BuildContext context, dynamic settings) {
    if (settings == null) return;

    context.read<SettingsBloc>().add(
          SettingsExportRequested(userId: settings.userId),
        );
  }

  void _importSettings(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Importer les données'),
        content:
            const Text('Cette fonctionnalité sera disponible prochainement.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('OK'),
          ),
        ],
      ),
    );
  }

  void _showAppInfo(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('MedyTrack v0.3.1'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('🎉 Enhanced Settings',
                style: TextStyle(fontWeight: FontWeight.bold)),
            SizedBox(height: 8),
            Text(
                '• Enhanced personalization with color pickers and icon selectors'),
            Text('• Interactive notification and language settings'),
            Text('• Data management and synchronization controls'),
            Text('• Professional UI with Material Design 3'),
            SizedBox(height: 16),
            Text('Développé avec Flutter 💙'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Fermer'),
          ),
        ],
      ),
    );
  }

  void _showTechInfo(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Technologies utilisées'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('🚀 Flutter Framework',
                style: TextStyle(fontWeight: FontWeight.bold)),
            Text('📱 Cross-platform mobile development'),
            SizedBox(height: 8),
            Text('🏗️ Architecture BLoC'),
            Text('🗄️ Supabase Backend'),
            Text('🎨 Material Design 3'),
            Text('🔄 GoRouter Navigation'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Fermer'),
          ),
        ],
      ),
    );
  }

  void _showAboutApp(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('À propos de MedyTrack'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('💊 MedyTrack',
                style: TextStyle(fontWeight: FontWeight.bold, fontSize: 18)),
            SizedBox(height: 8),
            Text(
                'Votre compagnon intelligent pour la gestion des médicaments familiaux.'),
            SizedBox(height: 16),
            Text('✨ Fonctionnalités principales:'),
            Text('• Suivi des dates d\'expiration'),
            Text('• Gestion des stocks'),
            Text('• Organisation par emplacements'),
            Text('• Gestion familiale'),
            Text('• Notifications personnalisées'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Fermer'),
          ),
        ],
      ),
    );
  }
}
