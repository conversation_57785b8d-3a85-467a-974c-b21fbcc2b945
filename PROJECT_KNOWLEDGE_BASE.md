# MedyTrack Mobile - Project Knowledge Base

**Version:** 0.4.5
**Last Updated:** 2025-01-30

## 1. Project Overview & Architecture

### 1.1. Application Purpose
- **Name**: MedyTrack Mobile
- **Purpose**: A comprehensive medication management application designed to help users track their medicines, set complex reminders, and monitor adherence.
- **Target Users**: Individuals and families managing multiple medications and complex schedules.
- **Core Functionality**:
    - Medicine Management (CRUD, inventory)
    - Family Member & Location Tracking
    - Advanced Reminder System (scheduling, notifications, dose history)
    - Dashboard for daily overview and statistics.
    - User Profile & Settings Personalization.

### 1.2. Architecture: Clean Architecture + BLoC
The project strictly follows **Clean Architecture** principles, separating concerns into three primary layers:
- **Domain Layer**: Contains core business logic, entities, and repository interfaces. It is the innermost layer and has no dependencies on other layers.
    - `lib/domain/entities/`: Core business objects (e.g., `reminder.dart`, `dose_history.dart`).
    - `lib/domain/repositories/`: Abstract contracts for data operations (e.g., `reminder_repository.dart`).
    - `lib/domain/usecases/`: Single-purpose classes that orchestrate data flow from repositories to the presentation layer.
- **Data Layer**: Implements the repository interfaces from the domain layer. It handles all data retrieval and storage, whether from a remote API or local cache.
    - `lib/data/models/`: Data Transfer Objects (DTOs) with `fromJson`/`toJson` for serialization (e.g., `reminder_model.dart`).
    - `lib/data/datasources/`: Concrete implementation of data fetching (e.g., `reminder_remote_data_source.dart` for Supabase).
    - `lib/data/repositories/`: Implementation of the repository interfaces (e.g., `reminder_repository_impl.dart`).
- **Presentation Layer**: Contains all UI-related components, including pages, widgets, and state management (BLoC).
    - `lib/presentation/bloc/`: BLoC classes for state management (e.g., `reminder_bloc/`).
    - `lib/presentation/pages/`: Top-level screen widgets.
    - `lib/presentation/widgets/`: Reusable UI components.

### 1.3. Backend: Supabase
- **Backend-as-a-Service (BaaS)**: Supabase is used for the database, authentication, and storage.
- **Database**: PostgreSQL.
- **Authentication**: Supabase Auth manages user registration, login, and session management.
- **Storage**: Used for storing user-uploaded assets like profile avatars.

### 1.4. UI/UX & Theming
- **Design System**: Material Design 3 (M3) is consistently applied across all components.
- **Theming**: A centralized theme is defined with `AppColors` and `AppTextStyles` for consistency.
- **Internationalization (i18n)**: The app supports multiple languages (French, English, Arabic) using Flutter's localization packages. All user-facing strings must be internationalized.

---

## 2. Technical Stack & Dependencies

### 2.1. Core Flutter Packages
- `flutter_bloc`: State management.
- `get_it` & `injectable`: Service locator and dependency injection.
- `go_router`: Declarative routing and navigation.
- `supabase_flutter`: Official Supabase client for Flutter.
- `freezed`: Code generation for immutable classes and unions (used for BLoC states).
- `json_serializable`: Automated JSON serialization/deserialization.
- `flutter_local_notifications`: For scheduling and displaying local device notifications.
- `equatable`: To compare objects by value, primarily in BLoC events and states.
- `intl`: For internationalization and date/time formatting.

### 2.2. Database Structure (PostgreSQL via Supabase)
Key tables include:
- **`medicines`**: Stores core medicine information.
- **`user_medicines`**: Links medicines to users, tracking inventory, location, family member, etc.
- **`reminders`**: Stores reminder configurations.
    - `id` (PK), `user_medicine_id` (FK), `frequency_type` (enum: `DAILY`, `WEEKLY`, `INTERVAL`), `times` (TEXT[]), `week_days` (INT[]), `interval_hours` (INT), `start_date`, `end_date`, `status` (enum: `ACTIVE`, `PAUSED`, `ARCHIVED`).
- **`dose_history`**: Tracks every dose action.
    - `id` (PK, auto-increment), `reminder_id` (FK), `user_medicine_id` (FK), `user_id` (FK), `scheduled_time`, `action_time`, `status` (enum: `TAKEN`, `SKIPPED`, `SNOOZED`), `notes`.

### 2.3. Row Level Security (RLS)
- **Policy**: All tables have RLS policies enabled.
- **Rule**: Policies ensure that users can only access and modify their own data. Queries must validate against `auth.uid()`.
- **Example Policy**: `CREATE POLICY "Enable read access for own reminders" ON reminders FOR SELECT USING (auth.uid() = user_id);`

### 2.4. Authentication System
- **Provider**: Supabase Auth.
- **Flow**: Standard email/password registration and login. Session is managed by `supabase_flutter`.
- **Integration**: The `user_id` from `auth.uid()` is a foreign key in all user-specific data tables to enforce RLS.

### 2.5. Notification Service
- **Package**: `flutter_local_notifications`.
- **Features**:
    - **Pre-Reminders**: Configurable notifications before a dose is due (e.g., 15 mins before).
    - **Post-Reminders**: Alerts for missed doses.
    - **Actions**: Interactive notifications with "Take", "Skip", and "Snooze" options.
    - **Snooze**: Flexible snooze intervals (5min, 15min, 30min, 1hr).
    - **Timezone Support**: All notifications are scheduled using timezone-aware logic.

---

## 3. Feature Documentation

### 3.1. Medicine Management
- **CRUD Operations**: Full support for adding, viewing, editing, and deleting medicines.
- **Inventory Tracking**: Manage stock quantity and receive low-stock alerts.
- **Search & Filter**: Advanced search by name and filtering by category, location, etc.
- **Details**: Each medicine can be associated with a family member, storage location, and custom tags.

### 3.2. Reminder System
- **Flexible Scheduling**:
    - **Daily**: Every day at specific times.
    - **Weekly**: On specific days of the week (e.g., Mon, Wed, Fri).
    - **Hourly Interval**: Every X hours (e.g., every 8 hours).
    - **Custom Date**: For non-recurring, one-off medications.
- **Status Management**: Reminders can be `ACTIVE`, `PAUSED`, or `ARCHIVED`. This is managed via toggle buttons on the reminders page.
- **Dose History**: Every `Take`, `Skip`, or `Snooze` action is logged in the `dose_history` table.

### 3.3. Dashboard
- **Today's Medicines**: A primary widget showing all scheduled doses for the current day.
- **Actions**: Users can directly perform `Take`, `Skip`, or `Snooze` actions from the dashboard.
- **Statistics**: Visual overview of adherence and other key metrics (placeholder for future implementation).

### 3.4. Settings
- **Profile Management**: Edit user name and avatar.
- **Security**: Change password, manage PIN, and enable/disable biometric authentication.
- **Personalization**:
    - Manage family members and storage locations (CRUD).
    - Configure notification timings and snooze intervals.
    - Set default expiry thresholds.
- **Data Management**: Export/import settings and user data.

---

## 4. Code Structure & Patterns

### 4.1. Directory & File Naming
- **Structure**: Follows the `domain`/`data`/`presentation` layer separation.
- **Feature-first**: Inside each layer, files are often grouped by feature.
    - Example: `lib/presentation/bloc/reminder/`, `lib/domain/usecases/reminder/`.
- **Naming Convention**:
    - Files: `snake_case.dart` (e.g., `add_reminder_page.dart`).
    - Classes: `PascalCase` (e.g., `ReminderBloc`).
    - BLoCs: `[Feature]Bloc.dart`, `[Feature]Event.dart`, `[Feature]State.dart`.

### 4.2. BLoC Event/State Pattern
- **Events**: Represent user actions or lifecycle events. They are classes extending a base `[Feature]Event` class.
    - Example: `AddReminder(Reminder reminder)`, `LoadReminders`.
- **States**: Represent the UI state. They are immutable classes, typically generated with `freezed` to create a sealed union.
    - Example: `ReminderState.initial()`, `ReminderState.loading()`, `ReminderState.loaded(List<Reminder> reminders)`, `ReminderState.error(String message)`.
- **Data Flow**:
    1. UI dispatches an `Event` to the `BLoC`.
    2. `BLoC` receives the `Event`, calls one or more `UseCases`.
    3. `UseCase` gets data from the `Repository`.
    4. `Repository` gets data from the `DataSource` (Supabase).
    5. Data flows back up the chain.
    6. `BLoC` emits a new `State` based on the result.
    7. UI rebuilds via a `BlocBuilder` or `BlocListener` listening to the state changes.

### 4.3. Entity, Model, and Serialization
- **Entity (`domain/entities`)**: A pure Dart object representing a core business concept. Contains no serialization logic.
- **Model (`data/models`)**: Extends an `Entity`. Contains `fromJson` and `toJson` methods for serialization. This is the object that interacts with the data sources.
- **Serialization**:
    - `json_serializable` is used to auto-generate serialization code.
    - **Conditional ID Exclusion**: When inserting new records into tables with auto-incrementing primary keys (like `dose_history`), the `id` field must be excluded from the JSON payload if it is `null`.
    ```dart
    // In a Model class
    @JsonSerializable(includeIfNull: false)
    class DoseHistoryModel extends DoseHistory {
      // ...
      Map<String, dynamic> toJson() => _$DoseHistoryModelToJson(this);
    }
    ```

### 4.4. UI Component Hierarchy
- **Pages (`presentation/pages`)**: Full-screen widgets that are targets for navigation. They are responsible for providing BLoCs to the widget tree.
- **Widgets (`presentation/widgets`)**:
    - **Common Widgets**: Reusable components used across multiple features (e.g., `AppHeader`, `BaseListCard`).
    - **Feature-Specific Widgets**: Components tied to a single feature (e.g., `reminder/frequency_selector.dart`).
- **Layout**: Widgets like `ScreenContainer` and `HeaderScreen` provide consistent padding and layout structure.

---

## 5. Development Guidelines

### 5.1. Coding Standards
- **Linting**: Adhere to `flutter_lints`.
- **Immutability**: States, Entities, and Models should be immutable. Use `copyWith` for modifications.
- **Clean Architecture**: Strictly maintain the separation of layers. The UI should only ever interact with a BLoC. A BLoC should only ever interact with UseCases.
- **Dependency Injection**: Do not instantiate Repositories or UseCases directly. Always retrieve them from the service locator (`get_it`).

### 5.2. Error Handling & Validation
- **Pattern**: Use cases return `Either<Failure, SuccessType>`. This forces the BLoC to handle both success and failure paths explicitly.
    - `Failure` is a custom class to encapsulate different error types (e.g., `ServerFailure`, `CacheFailure`).
- **UI Feedback**: The BLoC translates a `Failure` into a user-friendly error state (e.g., `ReminderState.error("Could not connect to the server.")`), which the UI then displays in a `SnackBar`, dialog, or inline message.
- **Input Validation**: Performed both on the client-side (in forms) and server-side (database constraints).
- **Graceful Degradation**: Use `try-catch` blocks extensively, especially in the Data layer, to prevent crashes from unexpected API responses or database errors.

### 5.3. Testing & Debugging
- **Test Suite**:
    - **Unit Tests**: For BLoCs, UseCases, and Models. Dependencies are mocked using `mockito`.
    - **Widget Tests**: For reusable widgets and pages to verify UI rendering and interaction.
    - **Integration Tests**: For end-to-end feature flows, including database interactions.
- **Debugging**:
    - **Logging**: A multi-level logging framework is in place to trace workflows from UI interaction down to database queries.
    - **Debug Pages**: The app contains special debug pages (disabled in production builds) for testing reminder actions, status transitions, and other core functionalities directly.

### 5.4. Cross-Platform Considerations
- **Target Platforms**: Android, iOS, and Web (Chrome).
- **Responsiveness**: UI must be responsive and adapt to different screen sizes. Use `LayoutBuilder`, `MediaQuery`, and flexible widgets.
- **Platform-Specific Code**: Keep platform-specific implementations to a minimum. If necessary, abstract them behind an interface.
- **Touch Targets**: Ensure all interactive elements have a minimum touch target size of 44x44dp for mobile and are easily clickable on the web.

---

## 6. Current State & Known Issues

### 6.1. Recently Implemented (v0.4.5)
- **Dashboard Action System**: The "Take/Skip/Snooze" buttons on the dashboard are fully functional. Critical database insertion issues (null `id`) have been resolved.
- **Advanced Notifications**: The full notification lifecycle (pre-reminder, post-reminder, snooze) is implemented.
- **Reminder Status Categorization**: The `Active`/`Paused`/`Archived` filtering system on the reminders page is functional.
- **Settings Page Overhaul**: Placeholders have been removed and replaced with functional settings for notifications and other features.

### 6.2. Hidden/Disabled Features
- **Debug Pages**: Accessible through a hidden gesture or developer menu. These pages MUST be disabled for production builds using `kReleaseMode` checks or similar compile-time flags.

### 6.3. Performance & Optimization
- **Database**: Indexes have been added to `reminders` and `dose_history` tables to optimize query performance.
- **UI Rendering**: List card heights are fixed (`104dp`) to improve list rendering performance.
- **State Management**: BLoCs are configured to avoid unnecessary rebuilds by leveraging `equatable` for state comparison.

### 6.4. Future Enhancement Roadmap
- **Advanced Analytics**: A dedicated dashboard for adherence reporting and insights.
- **Family Sharing**: Allow users to share reminders and medicine lists with family members.
- **Wearable Integration**: Support for Apple Watch and Android Wear (Wear OS).
- **AI-Powered Scheduling**: Suggest optimal reminder times based on user habits.
- **EHR/Pharmacy Integration**: Connect with healthcare systems for prescription management.

---