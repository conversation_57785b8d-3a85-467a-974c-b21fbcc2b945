import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';

import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../../l10n/generated/app_localizations.dart';
import '../../../domain/entities/medicine.dart';
import '../../../domain/entities/reminder.dart';
import '../../../core/utils/supabase_utils.dart';
import '../../bloc/medicine/medicine_bloc.dart';
import '../../bloc/reminder/reminder_bloc.dart';
import '../../bloc/reminder/reminder_state.dart';
import '../../bloc/reminder/reminder_event.dart';

class RemindersPage extends StatefulWidget {
  const RemindersPage({super.key});

  @override
  State<RemindersPage> createState() => _RemindersPageState();
}

class _RemindersPageState extends State<RemindersPage> {
  bool _isInitialLoad = true;
  int _selectedFilterIndex = 0; // 0: Active, 1: Paused, 2: Archived
  final List<bool> _filterSelections = [true, false, false];

  @override
  void initState() {
    super.initState();
    // Optimized loading: Load medicines first, then reminders automatically
    _loadData();
  }

  void _loadData() async {
    final householdId = SupabaseUtils.getHouseholdId(context);
    if (householdId != null) {
      // Load medicines first
      context
          .read<MedicineBloc>()
          .add(MedicineLoadRequested(householdId: householdId));
    }
  }

  void _loadReminders() {
    final medicineState = context.read<MedicineBloc>().state;
    if (medicineState is MedicineLoaded) {
      final medicineIds = medicineState.medicines.map((m) => m.id).toList();
      if (medicineIds.isNotEmpty) {
        context.read<ReminderBloc>().add(LoadBatchReminders(medicineIds));
      } else {
        context.read<ReminderBloc>().add(const ClearReminders());
      }
    } else {
      // If medicines are not loaded yet, wait for them to be loaded
      context.read<MedicineBloc>().stream.firstWhere((state) => state is MedicineLoaded).then((_) => _loadReminders());
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.teal,
      body: Column(
        children: [
          // Header with teal background
          Container(
            padding: const EdgeInsets.fromLTRB(20, 60, 20, 20),
            child: Row(
              children: [
                GestureDetector(
                  onTap: () => context.go('/dashboard'),
                  child: Container(
                    width: 48,
                    height: 48,
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Icon(
                      Icons.arrow_back,
                      color: Colors.white,
                      size: 24,
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Text(
                    'Rappels',
                    style: AppTextStyles.headlineMedium.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Content container with white background
          Expanded(
            child: Container(
              width: double.infinity,
              decoration: BoxDecoration(
                color: AppColors.white,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(24),
                  topRight: Radius.circular(24),
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 8,
                    offset: const Offset(0, -4),
                  ),
                ],
              ),
              child: BlocListener<ReminderBloc, ReminderState>(
                listener: (context, state) {
                  if (state is ReminderOperationSuccess) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(state.message),
                        backgroundColor: AppColors.success,
                      ),
                    );
                  } else if (state is ReminderError) {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: Text(state.message),
                        backgroundColor: AppColors.error,
                      ),
                    );
                  }
                },
                child: BlocBuilder<MedicineBloc, MedicineState>(
                  buildWhen: (previous, current) {
                    // Only rebuild when state actually changes
                    return previous != current;
                  },
                  builder: (context, medicineState) {
                    if (medicineState is MedicineLoading && _isInitialLoad) {
                      return const Center(child: CircularProgressIndicator());
                    } else if (medicineState is MedicineError) {
                      return _buildErrorState(medicineState.message);
                    } else if (medicineState is MedicineLoaded) {
                      // Auto-load reminders when medicines are loaded
                      if (_isInitialLoad) {
                        _isInitialLoad = false;
                        WidgetsBinding.instance.addPostFrameCallback((_) {
                          _loadReminders();
                        });
                      }

                      return BlocBuilder<ReminderBloc, ReminderState>(
                        buildWhen: (previous, current) {
                          // Only rebuild when reminder state changes meaningfully
                          return previous != current;
                        },
                        builder: (context, reminderState) {
                          return _buildRemindersContent(
                              medicineState.medicines, reminderState);
                        },
                      );
                    }
                    return const SizedBox.shrink();
                  },
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRemindersContent(
      List<Medicine> medicines, ReminderState reminderState) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Filter toggle buttons
          _buildFilterToggleButtons(),

          const SizedBox(height: 24),

          // Filtered reminders section
          _buildFilteredReminders(medicines, reminderState),
        ],
      ),
    );
  }

  Widget _buildFilterToggleButtons() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(horizontal: 4),
      child: ToggleButtons(
        isSelected: _filterSelections,
        onPressed: (int index) {
          setState(() {
            // Clear all selections
            for (int i = 0; i < _filterSelections.length; i++) {
              _filterSelections[i] = i == index;
            }
            _selectedFilterIndex = index;
          });
        },
        borderRadius: BorderRadius.circular(12),
        selectedBorderColor: AppColors.teal,
        selectedColor: Colors.white,
        fillColor: AppColors.teal,
        color: AppColors.grey600,
        constraints: const BoxConstraints(
          minHeight: 48,
          minWidth: 100,
        ),
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.check_circle_outline,
                  size: 18,
                  color:
                      _filterSelections[0] ? Colors.white : AppColors.grey600,
                ),
                const SizedBox(width: 8),
                Text(
                  'Actifs',
                  style: AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.w500,
                    color:
                        _filterSelections[0] ? Colors.white : AppColors.grey600,
                  ),
                ),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.pause_circle_outline,
                  size: 18,
                  color:
                      _filterSelections[1] ? Colors.white : AppColors.grey600,
                ),
                const SizedBox(width: 8),
                Text(
                  'En pause',
                  style: AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.w500,
                    color:
                        _filterSelections[1] ? Colors.white : AppColors.grey600,
                  ),
                ),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.archive_outlined,
                  size: 18,
                  color:
                      _filterSelections[2] ? Colors.white : AppColors.grey600,
                ),
                const SizedBox(width: 8),
                Text(
                  'Archivés',
                  style: AppTextStyles.bodyMedium.copyWith(
                    fontWeight: FontWeight.w500,
                    color:
                        _filterSelections[2] ? Colors.white : AppColors.grey600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFilteredReminders(
      List<Medicine> medicines, ReminderState reminderState) {
    if (reminderState is ReminderLoading && _isInitialLoad) {
      return const Center(child: CircularProgressIndicator());
    }
    
    List<Reminder> reminders = [];
    if (reminderState is RemindersLoaded) {
      reminders = reminderState.reminders;
    }

    if (_selectedFilterIndex == 0) {
      // Active reminders
      final activeReminders = reminders.where((r) => r.isCurrentlyActive).toList();
      if (activeReminders.isEmpty) return _buildEmptyReminderState();
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ...activeReminders.map((r) {
            Medicine medicine;
            try {
              medicine = medicines.firstWhere((m) => m.id == r.userMedicineId);
            } catch (e) {
              medicine = Medicine.unknown();
            }
            return _buildReminderCard(r, medicine);
          }),
          const SizedBox(height: 24),
          _buildAddReminderButton(),
        ],
      );
    } else if (_selectedFilterIndex == 1) {
      // Paused reminders
      final pausedReminders = reminders.where((r) => r.isPaused).toList();
      if (pausedReminders.isEmpty) {
        return _buildEmptyState(
          icon: Icons.pause_circle_outline,
          title: 'Aucun rappel en pause',
          subtitle: 'Les rappels mis en pause apparaîtront ici',
        );
      }
      return Column(
        children: pausedReminders.map((r) {
            Medicine medicine;
            try {
              medicine = medicines.firstWhere((m) => m.id == r.userMedicineId);
            } catch (e) {
              medicine = Medicine.unknown();
            }
            return _buildReminderCard(r, medicine, isPaused: true);
        }).toList(),
      );
    } else {
      // Archived reminders
      final archivedReminders = reminders.where((r) => r.isArchived).toList();
      if (archivedReminders.isEmpty) {
        return _buildEmptyState(
          icon: Icons.archive_outlined,
          title: 'Aucun rappel archivé',
          subtitle: 'Les rappels supprimés apparaîtront ici',
        );
      }
      return Column(
        children: archivedReminders.map((r) {
            Medicine medicine;
            try {
              medicine = medicines.firstWhere((m) => m.id == r.userMedicineId);
            } catch (e) {
              medicine = Medicine.unknown();
            }
            return _buildReminderCard(r, medicine, isArchived: true);
        }).toList(),
      );
    }
  }

  Widget _buildActiveReminderCards(
      List<Medicine> medicines, ReminderState reminderState) {
    // Extract active reminders from the state
    List<Reminder> activeReminders = [];
    if (reminderState is RemindersLoaded) {
      activeReminders =
          reminderState.reminders.where((r) => r.isCurrentlyActive).toList();
    }

    if (activeReminders.isEmpty) {
      return _buildEmptyReminderState();
    }

    return Column(
      children: activeReminders.map((reminder) {
        // Find the medicine for this reminder
        Medicine? foundMedicine;
        try {
          foundMedicine = medicines.firstWhere(
            (m) => m.id == reminder.userMedicineId,
          );
        } catch (e) {
          // Medicine not found, create a placeholder
          foundMedicine = Medicine.unknown();
        }
        final medicine = foundMedicine;

        return _buildReminderCard(reminder, medicine);
      }).toList(),
    );
  }

  Widget _buildEmptyReminderState() {
    return Card(
      elevation: 1,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            Icon(
              Icons.alarm_add,
              size: 48,
              color: AppColors.grey400,
            ),
            const SizedBox(height: 16),
            Text(
              'Aucun rappel configuré',
              style: AppTextStyles.titleMedium.copyWith(
                color: AppColors.grey600,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Commencez par ajouter des rappels pour vos médicaments',
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.grey500,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPausedReminderCards(
      List<Medicine> medicines, ReminderState reminderState) {
    // Extract paused reminders from the state
    List<Reminder> pausedReminders = [];
    if (reminderState is RemindersLoaded) {
      pausedReminders =
          reminderState.reminders.where((r) => r.isPaused).toList();
    }

    if (pausedReminders.isEmpty) {
      return _buildEmptyState(
        icon: Icons.pause_circle_outline,
        title: 'Aucun rappel en pause',
        subtitle: 'Les rappels mis en pause apparaîtront ici',
      );
    }

    return Column(
      children: pausedReminders.map((reminder) {
        // Find the medicine for this reminder
        Medicine? foundMedicine;
        try {
          foundMedicine = medicines.firstWhere(
            (m) => m.id == reminder.userMedicineId,
          );
        } catch (e) {
          foundMedicine = Medicine.unknown();
        }

        return _buildReminderCard(reminder, foundMedicine, isPaused: true);
      }).toList(),
    );
  }

  Widget _buildArchivedReminderCards(
      List<Medicine> medicines, ReminderState reminderState) {
    // Extract archived reminders from the state
    List<Reminder> archivedReminders = [];
    if (reminderState is RemindersLoaded) {
      archivedReminders =
          reminderState.reminders.where((r) => r.isArchived).toList();
    }

    if (archivedReminders.isEmpty) {
      return _buildEmptyState(
        icon: Icons.archive_outlined,
        title: 'Aucun rappel archivé',
        subtitle: 'Les rappels supprimés apparaîtront ici',
      );
    }

    return Column(
      children: archivedReminders.map((reminder) {
        // Find the medicine for this reminder
        Medicine? foundMedicine;
        try {
          foundMedicine = medicines.firstWhere(
            (m) => m.id == reminder.userMedicineId,
          );
        } catch (e) {
          foundMedicine = Medicine.unknown();
        }

        return _buildReminderCard(reminder, foundMedicine, isArchived: true);
      }).toList(),
    );
  }

  Widget _buildEmptyState({
    required IconData icon,
    required String title,
    required String subtitle,
  }) {
    return Card(
      elevation: 1,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          children: [
            Icon(
              icon,
              size: 48,
              color: AppColors.grey400,
            ),
            const SizedBox(height: 16),
            Text(
              title,
              style: AppTextStyles.titleMedium.copyWith(
                color: AppColors.grey600,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              subtitle,
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.grey500,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildReminderCard(Reminder reminder, Medicine medicine,
      {bool isPaused = false, bool isArchived = false}) {
    // Standardized card to match Today's Medicine card visual language
    return Card(
      elevation: 1,
      margin: const EdgeInsets.only(bottom: 12),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: const BorderSide(color: AppColors.grey200, width: 1),
      ),
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          InkWell(
            borderRadius: BorderRadius.circular(12),
            onTap: () {
              context.pushNamed(
                'reminder-detail',
                pathParameters: {'id': reminder.id ?? ''},
                extra: {
                  'reminder': reminder,
                  'medicine': medicine,
                },
              );
            },
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Grid-like content: Left text block; status pill moved to bottom-right
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Left: Two-row text block (name on first row, dosage/form on second row)
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Medicine name (adjusted font to mirror today's card emphasis)
                            Text(
                              medicine.name,
                              style: AppTextStyles.titleMedium.copyWith(
                                color: AppColors.navy,
                                fontWeight: FontWeight.bold,
                                fontSize:
                                    AppTextStyles.titleMedium.fontSize! + 2,
                              ),
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                            const SizedBox(height: 4),
                            // Dosage/form on its own row with reduced size
                            Text(
                              _composeDosageForm(medicine),
                              style: AppTextStyles.bodySmall.copyWith(
                                color: AppColors.black,
                                fontWeight: FontWeight.normal,
                                fontSize:
                                    AppTextStyles.bodySmall.fontSize! - 2,
                              ),
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(width: 12),
                      // Intentionally leave right-side free to avoid conflict with top-right Modify icon
                      const SizedBox.shrink(),
                    ],
                  ),
                  const SizedBox(height: 8),
                  // Schedule line (kept as-is under the two-row name + dosage/form)
                  Text(
                    _formatReminderSchedule(reminder),
                    style: AppTextStyles.bodyMedium.copyWith(
                      color: AppColors.grey600,
                    ),
                  ),
                  const SizedBox(height: 12),
                  // Actions row standardized like Today's Medicine card
                  Row(
                    children: [
                      // Column 1: Pause (bold text-only)
                      Expanded(
                        child: _textOnlyActionButtonBold(
                          label: 'Pause',
                          color: AppColors.grey600,
                          onTap: () => _pauseReminder(reminder, medicine),
                        ),
                      ),
                      const SizedBox(width: 8),
                      // Column 2: Archive (bold text-only)
                      Expanded(
                        child: _textOnlyActionButtonBold(
                          label: 'Archive',
                          color: AppColors.grey600,
                          onTap: () => _archiveReminder(reminder, medicine),
                        ),
                      ),
                      const SizedBox(width: 8),
                      // Column 3: Spacer (keeps 4-column rhythm)
                      const Expanded(
                        child: SizedBox(height: 36),
                      ),
                      const SizedBox(width: 8),
                      // Column 4: Status pill (Actif/En pause) aligned to right
                      Expanded(
                        child: Align(
                          alignment: Alignment.centerRight,
                          child: Container(
                            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                            decoration: BoxDecoration(
                              color: (reminder.isActive ? AppColors.success : AppColors.warning).withOpacity(0.1),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              reminder.isActive ? 'Actif' : 'En pause',
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                              style: AppTextStyles.bodySmall.copyWith(
                                color: reminder.isActive ? AppColors.success : AppColors.warning,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          // Modify icon at card corner (outside the content to avoid overlapping the status pill)
          Positioned(
            top: 0,
            right: 0,
            child: PopupMenuButton<String>(
              onSelected: (value) {
                if (value == 'modify') {
                  _editReminder(reminder, medicine);
                } else if (value == 'cancel') {
                  context.read<ReminderBloc>().add(DeleteReminder(reminder.id!));
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('Rappel pour ${medicine.displayName} supprimé'),
                      backgroundColor: AppColors.error,
                    ),
                  );
                }
              },
              itemBuilder: (context) => [
                PopupMenuItem<String>(
                  value: 'modify',
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(Icons.edit, color: Colors.black),
                      const SizedBox(width: 8),
                      Text('Modifier'),
                    ],
                  ),
                ),
                PopupMenuItem<String>(
                  value: 'cancel',
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(Icons.delete_outline, size: 18, color: AppColors.error),
                      const SizedBox(width: 8),
                      Text('Supprimer', style: TextStyle(color: AppColors.error)),
                    ],
                  ),
                ),
              ],
              icon: Container(
                width: 35,
                height: 35,
                decoration: BoxDecoration(
                  color: AppColors.navy,
                  borderRadius: const BorderRadius.only(
                    topRight: Radius.circular(12),
                    bottomLeft: Radius.circular(12),
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.08),
                      blurRadius: 6,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                alignment: Alignment.center,
                child: const Icon(Icons.more_vert, color: Colors.white, size: 18),
              ),
              padding: EdgeInsets.zero,
              shape: const RoundedRectangleBorder(
                borderRadius: BorderRadius.all(Radius.circular(8)),
              ),
              color: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  void _editReminder(Reminder reminder, Medicine medicine) {
    // Navigate to edit reminder page (similar to add reminder but with pre-filled data)
    context.push('/reminders/edit/${reminder.id}', extra: {
      'reminder': reminder,
      'medicine': medicine,
    });
  }

  // Compose dosage/form safely for second-row details
  String _composeDosageForm(Medicine medicine) {
    final parts = <String>[];
    if ((medicine.dosage ?? '').trim().isNotEmpty) parts.add(medicine.dosage!.trim());
    if ((medicine.form ?? '').trim().isNotEmpty) parts.add(medicine.form!.trim());
    return parts.isEmpty ? '' : parts.join(' ');
  }

  List<Widget> _buildStatusManagementButtons(
      Reminder reminder, Medicine medicine) {
    final List<Widget> buttons = [];

    switch (reminder.status) {
     case ReminderStatus.active:
       // Buttons rendered directly in _buildReminderCard as "Pause" and "Archive" (bold, text-only)
       break;

      case ReminderStatus.paused:
       // For paused, keep actions consistent: "Pause" becomes "Activer" and "Archive"
       buttons.addAll([
         Expanded(
           child: _textOnlyActionButtonBold(
             label: 'Activer',
             color: AppColors.success,
             onTap: () => _resumeReminder(reminder, medicine),
           ),
         ),
         const SizedBox(width: 8),
         Expanded(
           child: _textOnlyActionButtonBold(
             label: 'Archive',
             color: AppColors.grey600,
             onTap: () => _archiveReminder(reminder, medicine),
           ),
         ),
       ]);
       break;

      case ReminderStatus.archived:
       // Archived: "Activer" + "Supprimer"
       buttons.addAll([
         Expanded(
           child: _textOnlyActionButtonBold(
             label: 'Activer',
             color: AppColors.success,
             onTap: () => _resumeReminder(reminder, medicine),
           ),
         ),
         const SizedBox(width: 8),
         Expanded(
           child: _textOnlyActionButtonBold(
             label: 'Supprimer',
             color: AppColors.error,
             onTap: () => _deleteReminder(reminder, medicine),
           ),
         ),
       ]);
       break;
    }

    return buttons;
  }

 // Bold text-only action button builder matching today's card characteristics
 Widget _textOnlyActionButtonBold({
   required String label,
   required Color color,
   required VoidCallback onTap,
 }) {
   return GestureDetector(
     onTap: onTap,
     child: Container(
       height: 36,
       alignment: Alignment.center,
       child: Text(
         label,
         maxLines: 1,
         overflow: TextOverflow.ellipsis,
         style: AppTextStyles.labelSmall.copyWith(
           color: color,
           fontSize: 12,
           fontWeight: FontWeight.w700, // bold as required
         ),
       ),
     ),
   );
 }

 // Placeholder to preserve 4-column grid without adding extra CTA
 Widget _transparentHoldPlace() {
   return Container(
     height: 36,
     decoration: BoxDecoration(
       color: Colors.transparent,
       borderRadius: BorderRadius.circular(8),
     ),
   );
 }

  void _pauseReminder(Reminder reminder, Medicine medicine) {
    context.read<ReminderBloc>().add(PauseReminder(reminder.id!));
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Rappel pour ${medicine.name} mis en pause'),
        backgroundColor: AppColors.warning,
      ),
    );
  }

  void _resumeReminder(Reminder reminder, Medicine medicine) {
    context.read<ReminderBloc>().add(ResumeReminder(reminder.id!));
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Rappel pour ${medicine.name} réactivé'),
       backgroundColor: AppColors.success,
     ),
    );
  }

  void _archiveReminder(Reminder reminder, Medicine medicine) {
    context.read<ReminderBloc>().add(ArchiveReminder(reminder.id!));
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Rappel pour ${medicine.name} archivé'),
       backgroundColor: AppColors.grey600,
     ),
    );
  }

  void _deleteReminder(Reminder reminder, Medicine medicine) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
        icon: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: AppColors.error.withOpacity(0.1),
            shape: BoxShape.circle,
          ),
          child: Icon(
            Icons.delete_forever,
            color: AppColors.error,
            size: 32,
          ),
        ),
        title: Text(
          'Supprimer le rappel',
          style: AppTextStyles.titleLarge.copyWith(
            color: AppColors.grey900,
            fontWeight: FontWeight.w600,
          ),
          textAlign: TextAlign.center,
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Êtes-vous sûr de vouloir supprimer ce rappel pour ${medicine.displayName} ?',
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.grey600,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              'Cette action est irréversible.',
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.error,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              'Annuler',
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.grey600,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              // Delete the reminder
              context.read<ReminderBloc>().add(DeleteReminder(reminder.id!));
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.error,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: Text(
              'Supprimer',
              style: AppTextStyles.bodyMedium.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _formatReminderSchedule(Reminder reminder) {
    switch (reminder.frequencyType) {
      case 'DAILY':
        final times = reminder.times.join(', ');
        return 'Quotidien à $times';
      case 'WEEKLY':
        final days = reminder.frequencyDays.isNotEmpty
            ? reminder.frequencyDays.map((day) => _getDayName(day)).join(', ')
            : '';
        final times = reminder.times.join(', ');
        return '$days à $times';
      case 'HOURLY_INTERVAL':
        return 'Toutes les ${reminder.frequencyValue ?? 8} heures';
      case 'SPECIFIC_DATES':
        final times = reminder.times.join(', ');
        return 'Dates spécifiques à $times';
      default:
        final times = reminder.times.join(', ');
        return 'Rappel à $times';
    }
  }

  String _getDayName(int day) {
    switch (day) {
      case 1:
        return 'Lun';
      case 2:
        return 'Mar';
      case 3:
        return 'Mer';
      case 4:
        return 'Jeu';
      case 5:
        return 'Ven';
      case 6:
        return 'Sam';
      case 7:
        return 'Dim';
      default:
        return '';
    }
  }

  Widget _buildMedicinesWithoutReminders(List<Medicine> medicines) {
    if (medicines.isEmpty) {
      return Card(
        elevation: 1,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        child: Padding(
          padding: const EdgeInsets.all(20),
          child: Column(
            children: [
              Icon(
                Icons.medication_outlined,
                size: 48,
                color: AppColors.grey400,
              ),
              const SizedBox(height: 16),
              Text(
                'Aucun médicament',
                style: AppTextStyles.titleMedium.copyWith(
                  color: AppColors.grey600,
                  fontWeight: FontWeight.w500,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Ajoutez des médicaments pour configurer des rappels',
                style: AppTextStyles.bodyMedium.copyWith(
                  color: AppColors.grey500,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    return Column(
      children: medicines
          .take(3)
          .map((medicine) => _buildMedicineCard(medicine))
          .toList(),
    );
  }

  Widget _buildMedicineCard(Medicine medicine) {
    return Card(
      elevation: 1,
      margin: const EdgeInsets.only(bottom: 12),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: () {
          // TODO: Navigate to add reminder for this medicine
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Configurer rappel pour ${medicine.displayName}'),
              behavior: SnackBarBehavior.floating,
            ),
          );
        },
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: AppColors.teal.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(24),
                ),
                child: const Icon(
                  Icons.medication,
                  color: AppColors.teal,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      medicine.displayName,
                      style: AppTextStyles.titleMedium.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Aucun rappel configuré',
                      style: AppTextStyles.bodySmall.copyWith(
                        color: AppColors.grey600,
                      ),
                    ),
                  ],
                ),
              ),
              Icon(
                Icons.add_alarm,
                color: AppColors.teal,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildErrorState(String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: AppColors.error,
          ),
          const SizedBox(height: 16),
          Text(
            'Erreur de chargement',
            style: AppTextStyles.titleMedium,
          ),
          const SizedBox(height: 8),
          Text(
            message,
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.grey600,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _loadData,
            child: const Text('Réessayer'),
          ),
        ],
      ),
    );
  }

 // Snooze: record dose history entry (SNOOZED) with RLS-compliant user_id
 void _snoozeReminder(Reminder reminder, Medicine medicine) {
   final userId = SupabaseUtils.getUserId(context);
   if (userId == null) {
     ScaffoldMessenger.of(context).showSnackBar(
       const SnackBar(
         content: Text('Erreur: Utilisateur non authentifié'),
         backgroundColor: Colors.red,
       ),
     );
     return;
   }
   final now = DateTime.now();
   final dose = DoseHistory(
     userId: userId,
     userMedicineId: reminder.userMedicineId,
     reminderId: reminder.id,
     scheduledAt: now,
     actionAt: now,
     status: 'SNOOZED',
   );
   context.read<ReminderBloc>().add(AddDoseHistory(dose));
   ScaffoldMessenger.of(context).showSnackBar(
     SnackBar(
       content: Text('Rappel pour ${medicine.displayName} reporté'),
       backgroundColor: AppColors.warning,
     ),
   );
 }

 Widget _buildAddReminderButton() {
    final l10n = AppLocalizations.of(context)!;

    return Center(
      child: Container(
        width: double.infinity,
        margin: const EdgeInsets.symmetric(horizontal: 40),
        child: ElevatedButton.icon(
          onPressed: () {
            context.go('/reminders/add');
          },
          icon: const Icon(Icons.add_alarm),
          label: Text(l10n.addReminder),
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.teal,
            foregroundColor: Colors.white,
            padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 24),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            elevation: 2,
          ),
        ),
      ),
    );
  }
}