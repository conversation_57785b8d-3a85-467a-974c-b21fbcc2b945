import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';

import '../../../core/theme/app_colors.dart';
import '../../../core/theme/app_text_styles.dart';
import '../../bloc/auth/auth_bloc.dart';
import '../../bloc/auth/auth_event.dart';
import '../../bloc/auth/auth_state.dart' as auth_state;
import '../../widgets/common/form_components.dart';

/// Redesigned settings page with modern UI
class SettingsPageRedesign extends StatefulWidget {
  const SettingsPageRedesign({super.key});

  @override
  State<SettingsPageRedesign> createState() => _SettingsPageRedesignState();
}

class _SettingsPageRedesignState extends State<SettingsPageRedesign> {
  bool _notificationsEnabled = true;
  bool _reminderSounds = true;
  bool _darkMode = false;
  String _language = 'fr';

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.teal,
      body: Column(
        children: [
          // Header with teal background
          Container(
            padding: const EdgeInsets.fromLTRB(20, 60, 20, 20),
            child: Row(
              children: [
                GestureDetector(
                  onTap: () => context.pop(),
                  child: Container(
                    width: 48,
                    height: 48,
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: const Icon(
                      Icons.arrow_back,
                      color: Colors.white,
                      size: 24,
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Text(
                    'Paramètres',
                    style: AppTextStyles.headlineMedium.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // Content container with white background
          Expanded(
            child: Container(
              width: double.infinity,
              decoration: BoxDecoration(
                color: AppColors.white,
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(24),
                  topRight: Radius.circular(24),
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.1),
                    blurRadius: 8,
                    offset: const Offset(0, -4),
                  ),
                ],
              ),
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(20),
                child: Column(
                  children: [
                    // Profile section
                    _buildProfileSection(),

                    const SizedBox(height: 32),

                    // Settings sections
                    _buildNotificationSettings(),

                    const SizedBox(height: 24),

                    _buildAppearanceSettings(),

                    const SizedBox(height: 24),

                    _buildLanguageSettings(),

                    const SizedBox(height: 24),

                    _buildAccountSettings(),

                    const SizedBox(height: 24),

                    _buildSupportSettings(),

                    const SizedBox(height: 24),

                    // Debug settings
                    _buildDebugSettings(),

                    // const SizedBox(height: 24),

                    // Logout button
                    _buildLogoutButton(),

                    const SizedBox(height: 24),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProfileSection() {
    return BlocBuilder<AuthBloc, auth_state.AuthState>(
      builder: (context, authState) {
        String? userName;
        String? userEmail;
        String? avatarUrl;

        if (authState is auth_state.AuthAuthenticated) {
          userName = authState.user.displayName ?? 'Utilisateur';
          userEmail = authState.user.email;
          avatarUrl = null; // User entity doesn't have photoURL property
        }

        return Container(
          width: double.infinity,
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.06),
                blurRadius: 12,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Row(
            children: [
              // Avatar
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  color: AppColors.tealLight,
                  shape: BoxShape.circle,
                  border: Border.all(
                    color: AppColors.teal.withValues(alpha: 0.3),
                    width: 2,
                  ),
                ),
                child: avatarUrl != null
                    ? ClipOval(
                        child: Image.network(
                          avatarUrl,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) =>
                              _buildDefaultAvatar(),
                        ),
                      )
                    : _buildDefaultAvatar(),
              ),

              const SizedBox(width: 20),

              // User info
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      userName ?? 'Utilisateur',
                      style: AppTextStyles.titleLarge.copyWith(
                        color: AppColors.navy,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    if (userEmail != null) ...[
                      const SizedBox(height: 4),
                      Text(
                        userEmail,
                        style: AppTextStyles.bodyMedium.copyWith(
                          color: AppColors.grey600,
                        ),
                      ),
                    ],
                    const SizedBox(height: 12),
                    ModernButton(
                      text: 'Modifier le profil',
                      isOutlined: true,
                      onPressed: () {
                        // Navigate to profile edit
                      },
                    ),
                  ],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildDefaultAvatar() {
    return Icon(
      Icons.person,
      color: AppColors.teal,
      size: 40,
    );
  }

  Widget _buildNotificationSettings() {
    return _buildSettingsSection(
      title: 'Notifications',
      icon: Icons.notifications_outlined,
      children: [
        _buildSwitchTile(
          title: 'Activer les notifications',
          subtitle: 'Recevoir des rappels pour vos médicaments',
          value: _notificationsEnabled,
          onChanged: (value) {
            setState(() {
              _notificationsEnabled = value;
            });
          },
        ),
        _buildSwitchTile(
          title: 'Sons de rappel',
          subtitle: 'Jouer un son lors des notifications',
          value: _reminderSounds,
          onChanged: _notificationsEnabled
              ? (value) {
                  setState(() {
                    _reminderSounds = value;
                  });
                }
              : null,
        ),
      ],
    );
  }

  Widget _buildAppearanceSettings() {
    return _buildSettingsSection(
      title: 'Apparence',
      icon: Icons.palette_outlined,
      children: [
        _buildSwitchTile(
          title: 'Mode sombre',
          subtitle: 'Utiliser le thème sombre',
          value: _darkMode,
          onChanged: (value) {
            setState(() {
              _darkMode = value;
            });
          },
        ),
      ],
    );
  }

  Widget _buildLanguageSettings() {
    return _buildSettingsSection(
      title: 'Langue',
      icon: Icons.language_outlined,
      children: [
        _buildDropdownTile(
          title: 'Langue de l\'application',
          value: _language,
          items: const [
            DropdownMenuItem(value: 'fr', child: Text('Français')),
            DropdownMenuItem(value: 'en', child: Text('English')),
            DropdownMenuItem(value: 'ar', child: Text('العربية')),
          ],
          onChanged: (value) {
            if (value != null) {
              setState(() {
                _language = value;
              });
            }
          },
        ),
      ],
    );
  }

  Widget _buildAccountSettings() {
    return _buildSettingsSection(
      title: 'Compte',
      icon: Icons.account_circle_outlined,
      children: [
        _buildActionTile(
          title: 'Changer le mot de passe',
          subtitle: 'Modifier votre mot de passe',
          icon: Icons.lock_outline,
          onTap: () {
            // Navigate to change password
          },
        ),
        _buildActionTile(
          title: 'Gestion de la famille',
          subtitle: 'Gérer les membres de votre famille',
          icon: Icons.family_restroom_outlined,
          onTap: () {
            context.go('/family');
          },
        ),
        _buildActionTile(
          title: 'Exporter les données',
          subtitle: 'Télécharger vos données médicales',
          icon: Icons.download_outlined,
          onTap: () {
            // Export data functionality
          },
        ),
      ],
    );
  }

  Widget _buildSupportSettings() {
    return _buildSettingsSection(
      title: 'Support',
      icon: Icons.help_outline,
      children: [
        _buildActionTile(
          title: 'Centre d\'aide',
          subtitle: 'FAQ et guides d\'utilisation',
          icon: Icons.help_outline,
          onTap: () {
            // Navigate to help center
          },
        ),
        _buildActionTile(
          title: 'Nous contacter',
          subtitle: 'Envoyer un message au support',
          icon: Icons.email_outlined,
          onTap: () {
            // Contact support
          },
        ),
        _buildActionTile(
          title: 'À propos',
          subtitle: 'Version et informations légales',
          icon: Icons.info_outline,
          onTap: () {
            // Show about dialog
            _showAboutDialog();
          },
        ),
      ],
    );
  }

  Widget _buildDebugSettings() {
    return _buildSettingsSection(
      title: 'Développement',
      icon: Icons.bug_report_outlined,
      children: [
        _buildActionTile(
          title: 'Debug Rappels',
          subtitle: 'Tester les actions de rappel et historique',
          icon: Icons.medication_outlined,
          onTap: () {
            context.push('/debug/reminders');
          },
        ),
        _buildActionTile(
          title: 'Debug Médicaments',
          subtitle: 'Tester l\'ajout et la gestion des médicaments',
          icon: Icons.add_box_outlined,
          onTap: () {
            context.push('/debug/add-medicine');
          },
        ),
        _buildActionTile(
          title: 'Debug Cartes',
          subtitle: 'Tester l\'affichage des cartes médicaments',
          icon: Icons.credit_card_outlined,
          onTap: () {
            context.push('/debug/medicine-cards');
          },
        ),
        _buildActionTile(
          title: 'Nouveau flux rappel (Test)',
          subtitle: 'Ouvrir le flow multi-étapes de création de rappel',
          icon: Icons.alarm_add_outlined,
          onTap: () {
            context.push('/debug/add-reminder-test');
          },
        ),
      ],
    );
  }

  Widget _buildSettingsSection({
    required String title,
    required IconData icon,
    required List<Widget> children,
  }) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.06),
            blurRadius: 12,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Section header
          Padding(
            padding: const EdgeInsets.all(20),
            child: Row(
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: AppColors.tealLight.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    icon,
                    color: AppColors.teal,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Text(
                  title,
                  style: AppTextStyles.titleLarge.copyWith(
                    color: AppColors.navy,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),

          // Section content
          ...children,
        ],
      ),
    );
  }

  Widget _buildSwitchTile({
    required String title,
    required String subtitle,
    required bool value,
    required Function(bool)? onChanged,
  }) {
    return ListTile(
      title: Text(
        title,
        style: AppTextStyles.bodyLarge.copyWith(
          color: AppColors.navy,
          fontWeight: FontWeight.w500,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: AppTextStyles.bodyMedium.copyWith(
          color: AppColors.grey600,
        ),
      ),
      trailing: Switch(
        value: value,
        onChanged: onChanged,
        activeColor: AppColors.teal,
      ),
      contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 4),
    );
  }

  Widget _buildDropdownTile({
    required String title,
    required String value,
    required List<DropdownMenuItem<String>> items,
    required Function(String?)? onChanged,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
      child: ModernDropdown<String>(
        label: title,
        value: value,
        items: items,
        onChanged: onChanged,
      ),
    );
  }

  Widget _buildActionTile({
    required String title,
    required String subtitle,
    required IconData icon,
    required VoidCallback onTap,
  }) {
    return ListTile(
      leading: Icon(
        icon,
        color: AppColors.teal,
        size: 24,
      ),
      title: Text(
        title,
        style: AppTextStyles.bodyLarge.copyWith(
          color: AppColors.navy,
          fontWeight: FontWeight.w500,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: AppTextStyles.bodyMedium.copyWith(
          color: AppColors.grey600,
        ),
      ),
      trailing: Icon(
        Icons.arrow_forward_ios,
        color: AppColors.grey400,
        size: 16,
      ),
      onTap: onTap,
      contentPadding: const EdgeInsets.symmetric(horizontal: 20, vertical: 4),
    );
  }

  Widget _buildLogoutButton() {
    return SizedBox(
      width: double.infinity,
      child: ModernButton(
        text: 'Se déconnecter',
        isOutlined: true,
        textColor: AppColors.error,
        backgroundColor: AppColors.error,
        icon: Icons.logout,
        onPressed: () {
          _showLogoutConfirmation();
        },
      ),
    );
  }

  void _showLogoutConfirmation() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: Text(
          'Se déconnecter',
          style: AppTextStyles.titleLarge.copyWith(
            color: AppColors.navy,
            fontWeight: FontWeight.w600,
          ),
        ),
        content: Text(
          'Êtes-vous sûr de vouloir vous déconnecter ?',
          style: AppTextStyles.bodyLarge.copyWith(
            color: AppColors.grey600,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'Annuler',
              style: AppTextStyles.labelLarge.copyWith(
                color: AppColors.grey600,
              ),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              context.read<AuthBloc>().add(AuthSignOutRequested());
              context.go('/auth/login');
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.error,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: Text('Se déconnecter'),
          ),
        ],
      ),
    );
  }

  void _showAboutDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: Text(
          'À propos de MedyTrack',
          style: AppTextStyles.titleLarge.copyWith(
            color: AppColors.navy,
            fontWeight: FontWeight.w600,
          ),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Version 2.0.0',
              style: AppTextStyles.bodyLarge.copyWith(
                color: AppColors.grey600,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'MedyTrack vous aide à gérer vos médicaments et rappels de santé de manière simple et efficace.',
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.grey600,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(
              'Fermer',
              style: AppTextStyles.labelLarge.copyWith(
                color: AppColors.teal,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
