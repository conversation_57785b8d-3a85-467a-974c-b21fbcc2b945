# Generated code do not commit.
file(TO_CMAKE_PATH "C:\\Users\\<USER>\\Dev\\flutter" FLUTTER_ROOT)
file(TO_CMAKE_PATH "c:\\dev\\medytrack_mobile_v2" PROJECT_DIR)

set(FLUTTER_VERSION "0.3.1+3" PARENT_SCOPE)
set(FLUTTER_VERSION_MAJOR 0 PARENT_SCOPE)
set(FLUTTER_VERSION_MINOR 3 PARENT_SCOPE)
set(FLUTTER_VERSION_PATCH 1 PARENT_SCOPE)
set(FLUTTER_VERSION_BUILD 3 PARENT_SCOPE)

# Environment variables to pass to tool_backend.sh
list(APPEND FLUTTER_TOOL_ENVIRONMENT
  "FLUTTER_ROOT=C:\\Users\\<USER>\\Dev\\flutter"
  "PROJECT_DIR=c:\\dev\\medytrack_mobile_v2"
  "FLUTTER_ROOT=C:\\Users\\<USER>\\Dev\\flutter"
  "FLUTTER_EPHEMERAL_DIR=c:\\dev\\medytrack_mobile_v2\\windows\\flutter\\ephemeral"
  "PROJECT_DIR=c:\\dev\\medytrack_mobile_v2"
  "FLUTTER_TARGET=c:\\dev\\medytrack_mobile_v2\\lib\\main.dart"
  "DART_DEFINES=RkxVVFRFUl9WRVJTSU9OPTMuMzIuOA==,RkxVVFRFUl9DSEFOTkVMPXN0YWJsZQ==,RkxVVFRFUl9HSVRfVVJMPWh0dHBzOi8vZ2l0aHViLmNvbS9mbHV0dGVyL2ZsdXR0ZXIuZ2l0,RkxVVFRFUl9GUkFNRVdPUktfUkVWSVNJT049ZWRhZGE3YzU2ZQ==,RkxVVFRFUl9FTkdJTkVfUkVWSVNJT049ZWYwY2QwMDA5MQ==,RkxVVFRFUl9EQVJUX1ZFUlNJT049My44LjE="
  "DART_OBFUSCATION=false"
  "TRACK_WIDGET_CREATION=true"
  "TREE_SHAKE_ICONS=false"
  "PACKAGE_CONFIG=c:\\dev\\medytrack_mobile_v2\\.dart_tool\\package_config.json"
)
